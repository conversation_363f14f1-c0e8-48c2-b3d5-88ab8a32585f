import { Inject, Injectable } from '@nestjs/common'
import { EditorService } from './editor.service.js'
import { BaseIPCHandler } from '@/infra/types/BaseIPCHandler.js'
import { EditorIPCSchemas } from '@app/shared/types/ipc/editor.js'
import { z } from 'zod'

@Injectable()
export class EditorIpcHandler extends BaseIPCHandler<'editor'> {

  protected readonly platformPrefix = 'editor'

  constructor(
    @Inject(EditorService) private readonly editorService: EditorService
  ) {
    super()
  }

  registerAll() {
    // 生成组合
    this.registerHandler('generateCombos', async (data) => {
      // 输入验证
      const inputValidation = EditorIPCSchemas.generateCombos.input.safeParse(data)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.editorService.generateCombos(inputValidation.data)

      // 输出验证
      const outputValidation = EditorIPCSchemas.generateCombos.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })

    // 提取视频关键帧
    this.registerHandler('extractVideoKeyFrames', async (data) => {
      // 输入验证
      const inputValidation = EditorIPCSchemas.extractVideoKeyFrames.input.safeParse(data)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.editorService.extractVideoKeyFrames(inputValidation.data)

      // 输出验证
      const outputValidation = EditorIPCSchemas.extractVideoKeyFrames.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })

    // 保存编辑器状态
    this.registerHandler('saveEditorState', async (data) => {
      // 输入验证
      const inputValidation = EditorIPCSchemas.saveEditorState.input.safeParse(data)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.editorService.saveEditorState(inputValidation.data)

      // 输出验证
      const outputValidation = EditorIPCSchemas.saveEditorState.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })

    // 压缩编辑器状态数据
    this.registerHandler('compressEditorState', async (data) => {
      // 输入验证
      const inputValidation = EditorIPCSchemas.compressEditorState.input.safeParse(data)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.editorService.compressEditorState(inputValidation.data)

      // 输出验证
      const outputValidation = EditorIPCSchemas.compressEditorState.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })

    // 解压编辑器状态数据
    this.registerHandler('decompressEditorState', async (data) => {
      // 输入验证
      const inputValidation = EditorIPCSchemas.decompressEditorState.input.safeParse(data)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.editorService.decompressEditorState(inputValidation.data)

      // 输出验证
      const outputValidation = EditorIPCSchemas.decompressEditorState.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })

    // 上传混剪结果到服务器
    this.registerHandler('uploadMixcutResult', async (data) => {
      // 输入验证
      const inputValidation = EditorIPCSchemas.uploadMixcutResult.input.safeParse(data)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.editorService.uploadMixcutResult(inputValidation.data)

      // 输出验证
      const outputValidation = EditorIPCSchemas.uploadMixcutResult.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })

    // 获取音频文件的时长
    this.registerHandler('getAudioDuration', async (data) => {
      // 输入验证
      const inputValidation = EditorIPCSchemas.getAudioDuration.input.safeParse(data)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.editorService.getAudioDuration(inputValidation.data)

      // 输出验证
      const outputValidation = EditorIPCSchemas.getAudioDuration.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })
  }
}
