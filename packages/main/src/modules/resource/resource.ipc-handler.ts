import { Inject, Injectable } from '@nestjs/common'
import { ResourceService } from './resource.service.js'
import { BaseIPCHandler } from '@/infra/types/BaseIPCHandler.js'
import { ResourceIPCSchemas } from '@app/shared/types/ipc/resource.js'
import { z } from 'zod'

@Injectable()
export class ResourceIpcHandler extends BaseIPCHandler<'resource'> {

  protected readonly platformPrefix = 'resource'

  constructor(
    @Inject(ResourceService) private readonly service: ResourceService
  ) {
    super()
  }

  /**
   * 注册所有通用资源处理程序
   */
  registerAll(): void {
    // 通用资源获取，需要明确指定类型
    this.registerHandler('fetchOrSaveResource', async (data) => {
      // 输入验证
      const inputValidation = ResourceIPCSchemas.fetchOrSaveResource.input.safeParse(data)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.service.fetchOrSaveResource(inputValidation.data)

      // 输出验证
      const outputValidation = ResourceIPCSchemas.fetchOrSaveResource.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })

    // 缓存清理
    this.registerHandler('cleanResource', async (data) => {
      // 输入验证
      const inputValidation = ResourceIPCSchemas.cleanResource.input.safeParse(data)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.service.cleanResource(inputValidation.data)

      // 输出验证
      const outputValidation = ResourceIPCSchemas.cleanResource.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })

    // 获取资源信息
    this.registerHandler('getResource', async (data) => {
      // 输入验证
      const inputValidation = ResourceIPCSchemas.getResource.input.safeParse(data)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.service.getResource(inputValidation.data)

      // 输出验证
      const outputValidation = ResourceIPCSchemas.getResource.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })

    // 获取资源路径（不下载）
    this.registerHandler('getResourcePath', async (data) => {
      // 输入验证
      const inputValidation = ResourceIPCSchemas.getResourcePath.input.safeParse(data)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.service.getResourcePath(inputValidation.data)

      // 输出验证
      const outputValidation = ResourceIPCSchemas.getResourcePath.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })

    // 获取所有已缓存资源
    this.registerHandler('getAllResources', async () => {
      // 输入验证（无参数）
      const inputValidation = ResourceIPCSchemas.getAllResources.input.safeParse(undefined)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.service.getAllResources()

      // 输出验证
      const outputValidation = ResourceIPCSchemas.getAllResources.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })
  }
}
