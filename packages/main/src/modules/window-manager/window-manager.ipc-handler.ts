import { BaseIPCHandler } from '@/infra/types/BaseIPCHandler.js'
import { Injectable } from '@nestjs/common'
import { WindowIPCSchemas } from '@app/shared/types/ipc/window.js'
import { WindowManagerService } from './window-manager.service.js'
import { z } from 'zod'

@Injectable()
export class WindowManagerIpcHandler extends BaseIPCHandler<'windowManager'> {

  protected readonly platformPrefix = 'windowManager'

  constructor(
    private readonly windowManagerService: WindowManagerService
  ) {
    super()
  }

  registerAll() {
    // 打开开发者工具
    this.registerHandler('openDevTool', async () => {
      // 输入验证（无参数）
      const inputValidation = WindowIPCSchemas.openDevTool.input.safeParse(undefined)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.windowManagerService.openDevTool()

      // 输出验证
      const outputValidation = WindowIPCSchemas.openDevTool.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })

    // 最小化窗口
    this.registerHandler('minimize', async () => {
      // 输入验证（无参数）
      const inputValidation = WindowIPCSchemas.minimize.input.safeParse(undefined)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.windowManagerService.minimize()

      // 输出验证
      const outputValidation = WindowIPCSchemas.minimize.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })

    // 最大化窗口或还原窗口
    this.registerHandler('maximize', async () => {
      // 输入验证（无参数）
      const inputValidation = WindowIPCSchemas.maximize.input.safeParse(undefined)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.windowManagerService.maximize()

      // 输出验证
      const outputValidation = WindowIPCSchemas.maximize.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })

    // 关闭窗口并退出应用
    this.registerHandler('close', async () => {
      // 输入验证（无参数）
      const inputValidation = WindowIPCSchemas.close.input.safeParse(undefined)
      if (!inputValidation.success) {
        throw new Error(`输入参数验证失败: ${inputValidation.error.message}`)
      }

      const result = await this.windowManagerService.close()

      // 输出验证
      const outputValidation = WindowIPCSchemas.close.output.safeParse(result)
      if (!outputValidation.success) {
        throw new Error(`输出结果验证失败: ${outputValidation.error.message}`)
      }

      return result
    })
  }
}
