import { Injectable } from '@nestjs/common'
import { app, BrowserWindow } from 'electron'

/**
 * 窗口管理服务
 * 负责处理窗口相关的业务逻辑
 */
@Injectable()
export class WindowManagerService {

  /**
   * 打开开发者工具
   */
  async openDevTool(): Promise<void> {
    const win = BrowserWindow.getFocusedWindow()
    if (win) {
      win.webContents.openDevTools()
    }
  }

  /**
   * 最小化窗口
   */
  async minimize(): Promise<void> {
    const win = BrowserWindow.getFocusedWindow()
    if (win) {
      win.minimize()
    }
  }

  /**
   * 最大化窗口或还原窗口
   * @returns 当前是否为最大化状态
   */
  async maximize(): Promise<boolean> {
    const win = BrowserWindow.getFocusedWindow()
    if (!win) return false

    if (win.isMaximized()) {
      win.unmaximize()
      return false
    } else {
      win.maximize()
      return true
    }
  }

  /**
   * 关闭窗口并退出应用
   */
  async close(): Promise<void> {
    app.quit()
  }
}
