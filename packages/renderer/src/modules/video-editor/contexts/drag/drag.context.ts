import { createContext, ReactNode, RefObject, useContext } from 'react'
import { Overlay } from '@clipnest/remotion-shared/types'
import { GhostElement } from '../../types'

export class OverlaysAdjustment extends Map<
  /**
   * Overlay ID
   */
  number,
  {
    fromFrameShift?: number
    durationShift?: number
    targetStoryboardIndex?: number
  }
> {

  public apply(another: OverlaysAdjustment) {
    another.forEach((value, key) => {
      this.set(key, value)
    })
  }
}

export interface DraggableState {
  /**
   * 是否允许拖拽 (无论为何种 `DragAction`)
   */
  draggable: boolean

  /**
   * 拖拽导致的其他 Overlay 的变动
   */
  overlaysAdjust?: OverlaysAdjustment

  /**
   * 拖拽过后, 目标 Overlay 的起始帧
   */
  adjustedStartFrame?: number

  /**
   * 拖拽过后, 目标 Overlay 的总时长
   */
  adjustedDuration?: number

  /**
   * 拖拽过后, 目标 Overlay 所处的轨道序号
   */
  adjustedRow?: number

  /**
   * 拖拽过后, 目标 Overlay 所处的分镜序号
   */
  targetStoryboardIndex?: number
}

export type DragAction = 'move' | 'resize-end'

export interface OverlayDragInfo {
  overlay: Overlay

  initialFrom: number
  initialDurationInFrames: number
  initialRow: number

  action?: DragAction

  landingPoint?: GhostElement
  draggableState?: DraggableState

  currentFrom?: number
  currentDuration?: number
  currentRow?: number
}

export interface DragContextState {
  /**
   * 指示当前是否在进行拖拽
   */
  isDragging: boolean

  /**
   * 当前拖拽中的 Overlay
   */
  draggingOverlay: Overlay | null

  /**
   * 用于展示鼠标当前拖动位置
   */
  mousePosition: GhostElement | null

  /**
   * 用于指示当前拖拽的 Overlay 的最终落点
   */
  landingPoint: GhostElement | null

  /**
   * 用于预览即将会发生的 Overlay 飘移
   */
  previewOverlaysAdjust: OverlaysAdjustment

  /**
   * 指示当前鼠标指向的时间点
   */
  mouseOnCurrentFrame: number | null

  /**
   * 当前拖拽信息的引用
   */
  dragInfoRef: RefObject<OverlayDragInfo | null>
}

interface DragContextActions {
  setIsDragging: (isDragging: boolean) => void
  setMousePosition: (position: GhostElement | null) => void
  setLandingPoint: (point: GhostElement | null) => void
  setPreviewOverlaysAdjust: (adjust: OverlaysAdjustment) => void
  setMouseOnCurrentFrame: (frame: number | null) => void
  resetDragState: () => void

  /**
   * 处理 TimelineItem 拖拽结束/时长调整开始的公共处理方法
   */
  handleTimelineItemAdjustStart(overlay: Overlay): void

  /**
   * 处理 TimelineItem 拖拽结束/时长调整结束的公共处理方法
   */
  handleTimelineItemAdjustEnd(): void

  updateDraggableState(newVal: DraggableState): void
}

export type DragContextValues = DragContextState & DragContextActions

export const DragContext = createContext<DragContextValues | undefined>(undefined)

export interface DragProviderProps {
  children: ReactNode
}

export const useDragContext = (): DragContextValues => {
  const context = useContext(DragContext)
  if (context === undefined) {
    throw new Error('useDragContext must be used within a DragProvider')
  }
  return context
}
