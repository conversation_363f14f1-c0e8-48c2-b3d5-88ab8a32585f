import { useCallback, useRef } from 'react'
import { Overlay, TextOverlay } from '@clipnest/remotion-shared/types'
import { useEditorContext } from '../editor/context'
import { PIXELS_PER_FRAME } from '@/modules/video-editor/constants'
import { calculateTracksAfterOverlayUpdated } from '@/modules/video-editor/utils/track-helper'
import { IndexableTrack } from '../../types'
import { OverlaysAdjustment, useDragContext } from './drag.context'
import {
  buildOverlayUpdates,
  calculateDraggableStateForMoving,
  createOverlayFromResource,
  DroppableResource,
  getResourceMeta,
  ResourceMeta,
  snapToGrid
} from './drag.utils'
import { cacheManager } from '@/libs/cache/cache-manager'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { toast } from 'react-toastify'
import { calculateTextRenderInfo } from '@clipnest/overlay-renderer'
import { useTimelineContext } from '@/modules/video-editor/contexts'

interface ResourceDragInfo {
  isActive: boolean
  resource: DroppableResource & {
    meta: ResourceMeta
  }
  initialMouseX: number

  newOverlay: Overlay
  targetTrack: IndexableTrack | null
}

export interface ResourceDragHook {
  handleResourceDragStart(resource: DroppableResource, initialMouseX: number): void
  handleResourceDragMove(currentMouseX: number, targetTrack?: IndexableTrack): void
  handleResourceDragEnd(): void
}

/**
 * 资源区拖拽逻辑 Hook
 */
export const useResourceDrag = (): ResourceDragHook => {
  const { zoomScale, timelineGridRef } = useTimelineContext()

  const {
    tracks, getPlayerDimensions, updateTracks
  } = useEditorContext()

  const {
    dragInfoRef,
    setIsDragging,
    setMousePosition,
    setLandingPoint,
    setPreviewOverlaysAdjust,
    resetDragState,
    updateDraggableState
  } = useDragContext()

  const resourceDragInfo = useRef<ResourceDragInfo | null>(null)

  const handleResourceDragStart = useCallback(
    (resource: DroppableResource, initialMouseX: number) => {
      const meta = getResourceMeta(resource)

      if (!meta) {
        toast('无效的资源类型')
        return
      }

      const newOverlay = createOverlayFromResource(resource, 0, tracks, getPlayerDimensions())
      if (!newOverlay) {
        toast('无效的资源类型')
        return
      }

      resourceDragInfo.current = {
        isActive: true,
        resource: { ...resource, meta },
        initialMouseX,
        newOverlay,
        targetTrack: null,
      }

      // 拖动开始时预下载资源, 避免拖动到轨道上时的卡顿
      if (meta.type === ResourceType.FONT) {
        void cacheManager.font.cacheFont(meta.url)
      } else {
        void cacheManager.resource.cacheResource(
          meta.type,
          meta.url,
          undefined,
          meta.customExt || meta.filename?.split('.')?.pop()
        )
      }

      setIsDragging(true)
      setMousePosition(null)
      setLandingPoint(null)
      setPreviewOverlaysAdjust(new OverlaysAdjustment())
    },
    [tracks, getPlayerDimensions]
  )

  const handleResourceDragMove = useCallback(
    (currentMouseX: number, targetTrack?: IndexableTrack) => {
      if (!resourceDragInfo.current || !timelineGridRef.current) return

      // 不在轨道上，清除预览
      if (!targetTrack) {
        setMousePosition(null)
        setLandingPoint(null)
        setPreviewOverlaysAdjust(new OverlaysAdjustment())
        resourceDragInfo.current = {
          ...resourceDragInfo.current,
          targetTrack: null
        }
        return
      }

      const rect = timelineGridRef.current.getBoundingClientRect()
      const targetStartFrame = snapToGrid((currentMouseX - rect.left) / zoomScale / PIXELS_PER_FRAME)
      const newOverlay: Overlay = {
        ...resourceDragInfo.current.newOverlay,
        from: targetStartFrame
      }

      if (!dragInfoRef.current) {
        dragInfoRef.current = {
          overlay: newOverlay,
          initialFrom: 0,
          initialRow: 0,
          initialDurationInFrames: newOverlay.durationInFrames
        }
      }
      dragInfoRef.current.currentRow = targetTrack.index
      dragInfoRef.current.currentFrom = targetStartFrame

      updateDraggableState(
        calculateDraggableStateForMoving(
          tracks,
          newOverlay,
          targetStartFrame,
          targetTrack,
        )
      )
      resourceDragInfo.current = {
        ...resourceDragInfo.current,
        targetTrack,
      }
    },
    [tracks, zoomScale]
  )

  const handleResourceDragEnd = useCallback(
    async () => {
      if (!resourceDragInfo.current || !dragInfoRef.current) {
        return resetDragState()
      }

      const { draggableState } = dragInfoRef.current

      const { targetTrack, newOverlay, resource } = resourceDragInfo.current
      if (!resourceDragInfo.current || !draggableState || !targetTrack || !newOverlay || !resource) {
        return resetDragState()
      }

      const {
        adjustedDuration, adjustedStartFrame, targetStoryboardIndex, draggable, overlaysAdjust
      } = draggableState

      if (!draggable) return resetDragState()

      // TODO: 在此处显示一个 Loading
      const localSrc = resource && resource.meta
        ? await cacheManager.resource.waitForCachedResource(resource.meta.type, resource.meta.url)
        : undefined

      if (!localSrc) {
        toast('资源加载失败')
        return resetDragState()
      }

      const finalOverlay: Overlay = {
        ...newOverlay,
        localSrc,
        from: adjustedStartFrame ?? newOverlay.from,
        durationInFrames: adjustedDuration ?? newOverlay.durationInFrames,
        ...(targetStoryboardIndex !== undefined && { storyboardIndex: targetStoryboardIndex })
      }

      if (resource.meta.type === ResourceType.FONT) {
        const font = await cacheManager.font.cacheFont(resource.meta.url)

        if (!font) throw new Error('字体加载失败')

        const dimension = getPlayerDimensions()
        const { minWidth, minHeight } = calculateTextRenderInfo(font, finalOverlay as TextOverlay)
        finalOverlay.width = minWidth
        finalOverlay.height = minHeight
        finalOverlay.left = (dimension.playerWidth - minWidth) / 2
        finalOverlay.top = (dimension.playerHeight - minHeight) / 2
      }

      const updates = buildOverlayUpdates(tracks, overlaysAdjust)

      updateTracks(prevTracks => {
        const inserted = prevTracks.map((track, trackIndex) => {
          if (trackIndex === targetTrack.index) {
            return {
              ...track,
              overlays: [...track.overlays, finalOverlay]
            }
          }

          return track
        })

        return updates.reduce(
          (result, update) => calculateTracksAfterOverlayUpdated(result, update),
          inserted
        )
      })

      return resetDragState()
    },
    [tracks, updateTracks, getPlayerDimensions]
  )

  return {
    handleResourceDragStart,
    handleResourceDragMove,
    handleResourceDragEnd
  }
}
