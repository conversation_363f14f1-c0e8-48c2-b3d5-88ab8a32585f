import React, { useCallback, useRef, useState } from 'react'
import { Overlay } from '@clipnest/remotion-shared/types'
import { GhostElement } from '@/modules/video-editor/types'
import {
  DragContext,
  DragContextValues,
  DraggableState,
  DragProviderProps,
  OverlayDragInfo,
  OverlaysAdjustment
} from './drag.context'
import { SingleOverlayUpdatePayload } from '@/modules/video-editor/utils/track-helper'
import { buildOverlayUpdates } from '@/modules/video-editor/contexts/drag/drag.utils'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { getOverlayTrackIndex } from '@/modules/video-editor/utils/overlay-helper'
import _ from 'lodash'

export const DragProvider: React.FC<DragProviderProps> = ({ children }) => {
  const { tracks, bulkUpdateOverlays } = useEditorContext()

  const [isDragging, setIsDragging] = useState(false)
  const [landingPoint, setLandingPoint] = useState<GhostElement | null>(null)
  const [draggingOverlay, setDraggingOverlay] = useState<Overlay | null>(null)
  const [mousePosition, setMousePosition] = useState<GhostElement | null>(null)
  const [mouseOnCurrentFrame, setMouseOnCurrentFrame] = useState<number | null>(null)
  const [previewOverlaysAdjust, setPreviewOverlaysAdjust] = useState<OverlaysAdjustment>(new OverlaysAdjustment())

  const dragInfoRef = useRef<OverlayDragInfo | null>(null)

  const resetDragState = () => {
    setIsDragging(false)
    setDraggingOverlay(null)
    setLandingPoint(null)
    setMousePosition(null)
    setPreviewOverlaysAdjust(new OverlaysAdjustment())
    setMouseOnCurrentFrame(null)
    dragInfoRef.current = null
  }

  const handleTimelineItemAdjustStart = useCallback(
    (overlay: Overlay) => {
      const row = getOverlayTrackIndex(tracks, overlay.id) || 0

      setIsDragging(true)
      setDraggingOverlay(overlay)

      dragInfoRef.current = {
        overlay,
        initialFrom: overlay.from,
        initialDurationInFrames: overlay.durationInFrames,
        initialRow: row,
      }

      setMousePosition({
        from: overlay.from,
        durationInFrames: overlay.durationInFrames,
        row,
        overlay,
      })
      setPreviewOverlaysAdjust(new OverlaysAdjustment())
    },
    [tracks]
  )

  const handleTimelineItemAdjustEnd = useCallback(
    () => {
      const dragInfo = dragInfoRef.current

      if (
        !dragInfo
        || !dragInfo.landingPoint
        || !dragInfo.draggableState?.draggable
      ) {
        resetDragState()
        return
      }

      const { overlay: originalOverlay } = dragInfo

      if (!originalOverlay) {
        resetDragState()
        return
      }

      const predicatedRow = dragInfo.currentRow ?? dragInfo.initialRow

      const {
        overlaysAdjust, targetStoryboardIndex, draggable, adjustedStartFrame, adjustedDuration
      } = dragInfo.draggableState

      if (!draggable) {
        return resetDragState()
      }

      const itemsToUpdate: SingleOverlayUpdatePayload[] = [
        {
          ...originalOverlay,
          targetTrackIndex: predicatedRow,
          ...(adjustedStartFrame !== undefined && { from: adjustedStartFrame }),
          ...(adjustedDuration && { durationInFrames: adjustedDuration }),
          storyboardIndex: targetStoryboardIndex !== undefined
            ? targetStoryboardIndex
            : originalOverlay.storyboardIndex
        },
        ...buildOverlayUpdates(tracks, overlaysAdjust)
      ]

      bulkUpdateOverlays(itemsToUpdate)
      resetDragState()
    },
    [tracks, dragInfoRef, bulkUpdateOverlays, resetDragState]
  )

  const updateDraggableState  = (newState: DraggableState) => {
    const dragInfo = dragInfoRef.current
    if (!dragInfo) {
      return
    }

    dragInfo.draggableState = newState
    const {
      currentRow = dragInfo.initialRow,
      currentFrom = dragInfo.initialFrom,
      currentDuration = dragInfo.initialDurationInFrames,
    } = dragInfo

    const {
      draggable,
      adjustedRow = currentRow,
      adjustedStartFrame = currentFrom,
      adjustedDuration = currentDuration,
      overlaysAdjust = new OverlaysAdjustment()
    } = newState

    const mousePosition: GhostElement = {
      overlay: dragInfo.overlay,
      invalid: !draggable,
      row: currentRow,
      from: currentFrom,
      durationInFrames: currentDuration,
    }

    setMousePosition(mousePosition)

    if (draggable) {
      const newLandingPoint: GhostElement = _.merge({}, mousePosition, {
        from: adjustedStartFrame,
        durationInFrames: adjustedDuration,
        row: adjustedRow,
      })
      setLandingPoint(newLandingPoint)
      dragInfo.landingPoint = newLandingPoint
    } else {
      setLandingPoint(null)
    }

    setPreviewOverlaysAdjust(overlaysAdjust)
  }

  return (
    <DragContext.Provider
      value={{
        isDragging,
        draggingOverlay,
        mousePosition,
        landingPoint,
        previewOverlaysAdjust,
        mouseOnCurrentFrame,
        dragInfoRef,

        setIsDragging,
        setMousePosition,
        setLandingPoint,
        setPreviewOverlaysAdjust,
        setMouseOnCurrentFrame,
        resetDragState,
        updateDraggableState,

        handleTimelineItemAdjustStart,
        handleTimelineItemAdjustEnd,
      } as DragContextValues}
    >
      {children}
    </DragContext.Provider>
  )
}
