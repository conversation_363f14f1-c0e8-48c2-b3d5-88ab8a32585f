import React, { createContext, useC<PERSON>back, useContext } from 'react'

import { TimelineZoomHook } from './useTimelineZoom'
import { TimelineClipboard } from './useTimelineClipboard'
import { useTimelineSnapping } from './useTimelineSnapping'
import { TimelineTracksLayout } from './useTimelineTracksLayout'
import { TimelineOverlayActivation } from './useTimelineOverlayActivation'
import { DragContext, DragContextState } from '@/modules/video-editor/contexts/drag/drag.context'
import { GhostElement } from '@/modules/video-editor/types'
import { PIXELS_PER_FRAME } from '@/modules/video-editor/constants'

/**
 * Context interface for managing timeline state and interactions.
 * @interface TimelineContextType
 */
interface TimelineContextType extends
  TimelineZoomHook,
  TimelineOverlayActivation
{
  layout: TimelineTracksLayout
  clipboard: TimelineClipboard

  isContextMenuOpen: boolean,
  setIsContextMenuOpen(v: boolean): void

  /** Reference to the timeline grid DOM element */
  timelineGridRef: React.RefObject<HTMLDivElement | null>
}

/**
 * Context for sharing timeline state and functionality across components.
 */
export const TimelineContext = createContext<TimelineContextType>({} as any)

export const useTimelineContext = () => {
  const context = useContext(TimelineContext)
  if (!context) {
    throw new Error('useTimelineContext must be used within a TimelineProvider')
  }
  return context
}

type UseTimelineReturn = TimelineContextType & Omit<DragContextState, 'dragInfoRef'> & {
  landingPoint: GhostElement | null
  alignmentLines: number[]

  handleMouseMove(e: React.MouseEvent<HTMLDivElement>): void
}

export const useTimeline = (): UseTimelineReturn => {
  const timelineContext = useContext(TimelineContext)
  const dragContext = useContext(DragContext)

  if (!dragContext) throw new Error('Cannot find `DragContext`')
  if (!timelineContext) throw new Error('useTimeline must be used within a TimelineProvider')

  const { snappedLandingPoint, alignmentLines } = useTimelineSnapping(dragContext)

  const handleMouseMove = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      const { clientX } =  event
      const rect = timelineContext.timelineGridRef.current?.getBoundingClientRect()
      if (rect) {
        dragContext.setMouseOnCurrentFrame((clientX - rect.left) / PIXELS_PER_FRAME / timelineContext.zoomScale)
      }
      return
    },
    [timelineContext.zoomScale],
  )

  const { isDragging, draggingOverlay, mousePosition, previewOverlaysAdjust, mouseOnCurrentFrame } = dragContext

  return {
    ...timelineContext,
    alignmentLines,

    isDragging,
    draggingOverlay,
    mousePosition,
    previewOverlaysAdjust,
    mouseOnCurrentFrame,

    landingPoint: snappedLandingPoint,
    handleMouseMove,
  }
}
