import React, { useRef, useState } from 'react'
import { useTimelineClipboard } from './useTimelineClipboard'
import { useTimelineTracksLayout } from './useTimelineTracksLayout'
import { useTimelineZoom } from './useTimelineZoom'
import { useTimelineOverlayActivation } from './useTimelineOverlayActivation'
import { TimelineContext } from './timeline.context'

export const TimelineProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  const [isContextMenuOpen, setIsContextMenuOpen] = useState(false)

  const timelineGridRef = useRef<HTMLDivElement>(null)

  const clipboard = useTimelineClipboard()
  const layout = useTimelineTracksLayout()
  const zoom = useTimelineZoom(timelineGridRef)
  const overlaySelection = useTimelineOverlayActivation()

  return (
    <TimelineContext.Provider
      value={{
        ...zoom,
        ...overlaySelection,
        clipboard,
        layout,

        isContextMenuOpen,
        setIsContextMenuOpen,

        timelineGridRef,
      }}
    >
      {children}
    </TimelineContext.Provider>
  )
}
