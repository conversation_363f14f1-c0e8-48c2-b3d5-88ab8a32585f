import { useCallback } from 'react'
import { OverlayType, SoundOverlay } from '@clipnest/remotion-shared/types'
import { cacheManager } from '@/libs/cache/cache-manager'
import { useOverlayHelper } from './helpers/useOverlayHelper'
import { ResourceType } from '@app/shared/types/resource-cache.types'

export function useAddAudioToTimeLine() {
  const { addOverlayToGlobalTrack } = useOverlayHelper()

  const handleAddAudioToTimeline = useCallback(
    async (durationMsec, itemUrl, title) => {
      try {
        const localPath = cacheManager.resource.getResourcePathSync(ResourceType.SOUND, itemUrl)
        const musicDurationInFrames = Math.round((durationMsec / 1000) * 30)
        const src = localPath ? localPath : itemUrl
        const overlay: SoundOverlay = {
          id: Date.now(),
          type: OverlayType.SOUND,
          content: title,
          src: src,
          localSrc: src,
          durationInFrames: musicDurationInFrames,
          from: 0,
          height: 100,
          width: 200,
          left: 0,
          top: 0,
          isDragging: false,
          rotation: 0,
          styles: {
            volume: 1,
          },
        }

        addOverlayToGlobalTrack(overlay)
        console.log('添加音乐到时间轴')
      } catch (error) {
        console.error('添加音乐到时间轴失败:', error)
      }
    },
    [addOverlayToGlobalTrack],
  )

  return { handleAddAudioToTimeline }
}
