import React, { useEffect, useState } from 'react'
import { useParams, useSearchParams } from 'react-router'
import { Music, RefreshCw } from 'lucide-react'
import { MaterialResource, ResourceSource } from '@/types/resources'
import TreeList from '@/components/TreeList'
import MoveDialog from './components/MoveDialog'
import MediaTypeSelector from './components/MediaTypeSelector'
import MaterialFilterBar from './components/MaterialFilterBar'
import MediaItem from './components/MediaItem'
import Breadcrumbs from '@/components/Breadcrumbs'
import UploadMaterial from './components/UploadMaterial'
import { useItemActions } from '@/hooks/useItemActions'
import { useFolderActions } from '@/hooks/useFolderActions'
import { useMediaActions } from '@/hooks/useMediaActions'
import { DndContext, DragOverlay, } from '@dnd-kit/core'
import folderIcon from '@/assets/folder.svg'
import { AuthedImg } from '@/components/authed-img'
import { toast } from 'react-toastify'
import { useMaterialManager } from '@/hooks/useMaterialManager'
import BatchActionButtons from './components/BatchActionButtons'
import {
  centerDragOverlay,
  DraggableItem,
  DroppableItem,
  pointerWithinFolder,
  useFolderDndSensors,
  useFolderDragEnd
} from '@/components/folder-dnd'

const Material: React.FC = () => {
  const params = useParams()
  const [searchParams] = useSearchParams()

  const [activeTab, setActiveTab] = useState(0)
  const [orientation, setOrientation] = useState(MaterialResource.MediaStyle.HORIZONTAL)
  const [moveDialogOpen, setMoveDialogOpen] = useState(false)
  const [moveId, setMoveId] = useState('')
  const [moveType, setMoveType] = useState(ResourceSource.FOLDER)

  const [activeMedia, setActiveMedia] = useState<any | null>(null)
  const sensors = useFolderDndSensors()
  const handleDragEnd = useFolderDragEnd(setActiveMedia)

  const { createItem } = useItemActions()
  const folderActions = useFolderActions(false, ResourceSource.FOLDER, setMoveType, setMoveId, setMoveDialogOpen)
  const mediaActions = useMediaActions(false, ResourceSource.MEDIA, setMoveType, setMoveId, setMoveDialogOpen)

  const materialManager = useMaterialManager({
    projectId: Number(params.projectId),
    initialKeyword: searchParams.get('keyword') || '',
  })

  const {
    filters,
    setFilters,
    treeData,
    currentFolderId,
    folderPath,
    filteredFolders,
    mediaQueryResult: { data: mediaList, isLoading },
    selection,
    onRefresh,
    handleBatchDelete,
    handleFolderClick,
    handleMoveConfirm,
  } = materialManager

  const handleDragCancel = () => {
    setActiveMedia(null) // 拖拽取消清空预览图片
  }

  useEffect(() => {
    const searchKey = searchParams.get('keyword') || ''
    setFilters(prevFilters => ({
      ...prevFilters,
      keyword: searchKey,
    }))
  }, [searchParams, filteredFolders])

  return (
    <div className="p-4 flex flex-col flex-1 h-full w-full overflow-auto">
      <MediaTypeSelector
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        setFilters={setFilters}
        onCreateFolder={() => createItem(ResourceSource.FOLDER, currentFolderId, {
          label: '文件夹名称',
          headerTitle: '文件夹',
        })}
      />

      <MaterialFilterBar
        filters={filters}
        setFilters={setFilters}
        orientation={orientation}
        setOrientation={setOrientation}
      />

      <div className="border-t mt-2 mb-3" />

      <div className="flex flex-1 overflow-auto">
        {/* 目录树 */}
        <TreeList
          data={treeData}
          defaultExpandAll={true}
          className="w-64 flex-shrink-0 overflow-auto border-r"
          actions={folderActions}
          showEllipsis={true}
          selectedId={currentFolderId}
          onSelect={node => {
            handleFolderClick(node.id)
          }}
        />
        <div className="flex-1 overflow-auto">
          <div className="flex items-center justify-between text-sm text-gray-600 pl-4">
            {/* 面包屑导航 */}
            <Breadcrumbs folderPath={folderPath} currentFolderId={currentFolderId} onFolderClick={handleFolderClick} />
            <div className="flex justify-end items-center space-x-4">
              {selection.selectedCount !== 0 && (
                // 多选-移动到/删除
                <BatchActionButtons
                  variant="default"
                  highlightClass="bg-primary-highlight1 hover:bg-blue-400 text-white"
                  deleteName={selection.selectedText}
                  onMove={() => {
                    setMoveType(ResourceSource.MULTI_SELECT)
                    setMoveDialogOpen(true)
                  }}
                  onDelete={handleBatchDelete}
                />
              )}

              {/* select */}
              <span>素材总数：{selection.materialCount}</span>
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={selection.allSelected}
                  onChange={selection.toggleSelectAll}
                  className="mr-1 accent-primary-highlight1"
                />
                全选
              </label>
              <span> | </span>
              <span>已选 {selection.selectedCount}</span>
              <div className="flex items-center">
                <RefreshCw className="inline-block w-4 h-4 mr-1 text-primary-highlight1" />
                <button
                  className="hover:underline"
                  onClick={async () => {
                    await onRefresh()
                    toast('刷新成功', { type: 'success' })
                  }}
                >
                  刷新
                </button>
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center h-full">加载中...</div>
          ) : (
            <div className="flex flex-wrap gap-4 p-4 overflow-auto">
              {/* 上传 */}
              <UploadMaterial mode="card" folderUuid={currentFolderId} orientation={orientation} onUpload={onRefresh} />
              <DndContext
                onDragStart={event => {
                  const { active } = event
                  if (active.data.current) {
                    setActiveMedia(active.data.current) // 记录正在拖拽的 media
                  }
                }}
                onDragCancel={handleDragCancel}
                onDragEnd={handleDragEnd}
                sensors={sensors}
                collisionDetection={pointerWithinFolder}
              >
                {/* 文件夹列表 */}
                {filteredFolders.map(folder => (
                  <DroppableItem key={`${folder.fileId}-folders`} id={folder.fileId} type={ResourceSource.FOLDER}>
                    <DraggableItem<MaterialResource.Media>
                      key={`${folder.fileId}-folder`}
                      id={`${folder.fileId}`}
                      resource={folder}
                      type={ResourceSource.FOLDER}
                    >
                      <MediaItem
                        key={folder.fileId}
                        data-type="folder"
                        orientation={orientation}
                        media={folder}
                        isSelected={selection.selectedFolderItems.has(folder.fileId)}
                        isFolder={true}
                        actions={folderActions}
                        onToggleSelect={fileId => selection.toggleSelect(fileId, true)}
                        onFolderClick={() => handleFolderClick(folder.fileId)}
                      />
                    </DraggableItem>
                  </DroppableItem>
                ))}

                {/* 媒体资源列表 */}
                {mediaList?.pages.map(page =>
                  page.list.map(media => (
                    <DraggableItem<MaterialResource.Media>
                      key={`${media.fileId}-media`}
                      id={media.fileId}
                      resource={media}
                      type={ResourceSource.MEDIA}
                    >
                      <MediaItem
                        key={media.fileId}
                        data-type="media"
                        orientation={orientation}
                        media={media}
                        isSelected={selection.selectedMediaItems.has(media.fileId)}
                        isFolder={false}
                        actions={mediaActions}
                        onToggleSelect={fileId => selection.toggleSelect(fileId, false)}
                      />
                    </DraggableItem>
                  )),
                )}
                {/* 拖拽时的预览图片 */}
                <DragOverlay modifiers={[centerDragOverlay]} dropAnimation={null}>
                  {activeMedia ? (
                    activeMedia.resType === MaterialResource.MediaType.FOLDER ? (
                      // 📁 文件夹类型
                      <img src={folderIcon} alt="Folder" className="w-24 h-24" />
                    ) : activeMedia.resType === MaterialResource.MediaType.VIDEO ? (
                      // 🎬 视频类型
                      <AuthedImg src={activeMedia.cover} alt={activeMedia.fileName} />
                    ) : activeMedia.resType === MaterialResource.MediaType.AUDIO ? (
                      // 🎵 音频类型
                      <div className="w-24 h-24 flex items-center justify-center bg-gray-200 rounded shadow-lg">
                        <Music className="w-[40%] h-[40%]" />
                      </div>
                    ) : activeMedia.resType === MaterialResource.MediaType.IMAGE ? (
                      // 🖼 图片类型
                      <AuthedImg src={activeMedia.cover} alt={activeMedia.fileName} />
                    ) : null
                  ) : null}
                </DragOverlay>
              </DndContext>
            </div>
          )}
        </div>
      </div>
      
      <MoveDialog
        open={moveDialogOpen}
        moveId={moveId}
        moveType={moveType}
        onOpenChange={setMoveDialogOpen}
        onConfirm={selectedNode => handleMoveConfirm(selectedNode, moveType)}
      />
    </div>
  )
}

export default Material
