import { ipc<PERSON>enderer } from 'electron'
import IPCClients from '@app/shared/types/ipc-clients.js'
import { CrudableIpcClient } from '@app/shared/types/crudable-ipc-client.js'
import { z } from 'zod'

/**
 * 创建类型安全的IPC客户端
 * @param key 平台前缀
 * @returns 类型安全的IPC客户端对象
 */
export function createTypedIPCClient<Key extends keyof IPCClients, Client = IPCClients[Key]>(
  key: Key,
): Client {
  // 递归创建代理对象，支持嵌套属性
  function createProxy(path: string[] = []): any {
    return new Proxy({}, {
      get: (_target, prop: string) => {
        const newPath = [...path, prop]

        // 返回一个函数，同时这个函数也是一个代理对象，支持进一步的属性访问
        const fn = (...args: any[]) => {
          const channel = `${key as string}:${newPath.join('.')}`
          return ipcRenderer.invoke(channel, ...args)
        }

        // 让函数也可以作为对象，支持进一步的属性访问
        return new Proxy(fn, {
          get: (_target, nextProp: string) => {
            return createProxy(newPath)[nextProp]
          }
        })
      }
    })
  }

  const proxy = createProxy() as Client

  // TODO: wrap `exposeInMainWorld` here?

  return proxy
}

/**
 * 创建带有 schema 验证的 IPC 客户端
 * @param key 平台前缀
 * @param schemas Schema 定义对象
 * @returns 带有验证的类型安全 IPC 客户端对象
 */
export function createValidatedIPCClient<
  Key extends keyof IPCClients,
  Schemas extends Record<string, { input: z.ZodTypeAny; output: z.ZodTypeAny }>
>(
  key: Key,
  schemas: Schemas
): {
  [K in keyof Schemas]: (
    ...args: Schemas[K]['input'] extends z.ZodVoid
      ? []
      : [z.infer<Schemas[K]['input']>]
  ) => Promise<z.infer<Schemas[K]['output']>>
} {
  const client = {} as any

  for (const methodName in schemas) {
    const schema = schemas[methodName]

    client[methodName] = async (...args: any[]) => {
      const channel = `${key as string}:${methodName}`

      // 输入验证
      const input = args.length === 0 ? undefined : args[0]
      const inputValidation = schema.input.safeParse(input)
      if (!inputValidation.success) {
        throw new Error(`IPC 调用 ${channel} 输入参数验证失败: ${inputValidation.error.message}`)
      }

      // 调用 IPC
      const result = await ipcRenderer.invoke(channel, ...args)

      // 输出验证
      const outputValidation = schema.output.safeParse(result)
      if (!outputValidation.success) {
        console.warn(`IPC 调用 ${channel} 输出结果验证失败: ${outputValidation.error.message}`)
        // 注意：这里只是警告，不抛出错误，因为服务端可能返回了有效但不符合 schema 的数据
      }

      return result
    }
  }

  return client
}

export function exposeCrudable(client: CrudableIpcClient<any, any, any, any, any>) {
  return {
    create: (v: any) => client.create(v),
    get: (v: any) => client.get(v),
    list: (v: any) => client.list(v),
    update: (v: any) => client.update(v),
    delete: (v: any) => client.delete(v),
    restore: (v: any) => client.restore(v),
    permanentlyDelete: (v: any) => client.permanentlyDelete(v),
    batchDelete: (v: any) => client.batchDelete(v),
    batchPermanentlyDelete: (v: any) => client.batchPermanentlyDelete(v),
  }
}
