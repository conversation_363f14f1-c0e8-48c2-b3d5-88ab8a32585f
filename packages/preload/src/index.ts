import { sha256sum } from './nodeCrypto.js'
import { versions } from './versions.js'
import { contextBridge, ipcRenderer } from 'electron'
import { createTypedIPCClient, exposeCrudable, createValidatedIPCClient } from './ipc-client-utils.js'
import { WindowIPCSchemas } from '@app/shared/types/ipc/window.js'

const editor = createTypedIPCClient('editor')
contextBridge.exposeInMainWorld('editor', {
  generateCombos: (v: any) => editor.generateCombos(v),
  extractVideoKeyFrames: (v: any) => editor.extractVideoKeyFrames(v),
  compressEditorState: (v: any) => editor.compressEditorState(v),
  decompressEditorState: (v: any) => editor.decompressEditorState(v),
  uploadMixcutResult: (v: any) => editor.uploadMixcutResult(v),
  getAudioDuration: (v: any) => editor.getAudioDuration(v),
})

const resource = createTypedIPCClient('resource')
contextBridge.exposeInMainWorld('resource', {
  fetchOrSaveResource: (v: any) => resource.fetchOrSaveResource(v),
  cleanResource: (v: any) => resource.cleanResource(v),
  getResource: (v: any) => resource.getResource(v),
  getResourcePath: (v: any) => resource.getResourcePath(v),
  getAllResources: () => resource.getAllResources(),
})

const fileUploader = createTypedIPCClient('fileUploader')
contextBridge.exposeInMainWorld('fileUploader', {
  uploadBufferToOSS: (v: any) => fileUploader.uploadBufferToOSS(v),
})

const fileDownloader = createTypedIPCClient('fileDownloader')
contextBridge.exposeInMainWorld('fileDownloader', {
  selectFolder: (v: any) => fileDownloader.selectFolder(v),
  selectFile: (v: any) => fileDownloader.selectFile(v),
  fileExists: (v: any) => fileDownloader.fileExists(v),
  downloadFiles: (v: any) => fileDownloader.downloadFiles(v),
})

const baseInfo = createTypedIPCClient('baseInfo')
contextBridge.exposeInMainWorld('baseInfo', {
  updateRequestState: (v: any) => baseInfo.updateRequestState(v),
  getRequestState: () => baseInfo.getRequestState(),
  clearRequestState: () => baseInfo.clearRequestState(),
  isTokenExpired: () => baseInfo.isTokenExpired(),
})

const autoUpdater = createTypedIPCClient('autoUpdater')
contextBridge.exposeInMainWorld('autoUpdater', {
  checkForUpdates: () => autoUpdater.checkForUpdates(),
  startUpdate: () => autoUpdater.startUpdate(),
  installUpdate: () => autoUpdater.installUpdate(),
})

// 使用新的验证客户端创建 windowManager
const windowClient = createValidatedIPCClient('windowManager', WindowIPCSchemas)
contextBridge.exposeInMainWorld('windowManager', {
  openDevTool: () => windowClient.openDevTool(),
  minimize: () => windowClient.minimize(),
  maximize: () => windowClient.maximize(),
  close: () => windowClient.close(),
})

//#region ~ Crudable Clients
const uploadTask = createTypedIPCClient('uploadTask')
contextBridge.exposeInMainWorld('uploadTask', {
  ...exposeCrudable(uploadTask),
  getUserTasks: (v: any) => uploadTask.getUserTasks(v),
  getTasksByFolder: (v: any) => uploadTask.getTasksByFolder(v),
  searchTasks: (v: any) => uploadTask.searchTasks(v),
  getTaskStats: (v: any) => uploadTask.getTaskStats(v),
  startUpload: (v: any) => uploadTask.startUpload(v),
  pauseUpload: (v: any) => uploadTask.pauseUpload(v),
  resumeUpload: (v: any) => uploadTask.resumeUpload(v),
  cancelUpload: (v: any) => uploadTask.cancelUpload(v),
  retryUpload: (v: any) => uploadTask.retryUpload(v),
  batchOperation: (v: any) => uploadTask.batchOperation(v),
  getQueueConfig: () => uploadTask.getQueueConfig(),
  updateQueueConfig: (v: any) => uploadTask.updateQueueConfig(v),
  getQueueStatus: () => uploadTask.getQueueStatus(),
  cleanupCompleted: (v: any) => uploadTask.cleanupCompleted(v),
  uploadFileContent: (v: any) => uploadTask.uploadFileContent(v),
  selectFiles: (v: any) => uploadTask.selectFiles(v),
  uploadFromPath: (v: any) => uploadTask.uploadFromPath(v),
})
//#endregion

// 暴露 IPC 事件监听功能
contextBridge.exposeInMainWorld('electronAPI', {
  ipcRenderer: {
    on: (channel: string, callback: (...args: any[]) => void) => {
      ipcRenderer.on(channel, callback)
    },
    removeListener: (channel: string, callback: (...args: any[]) => void) => {
      ipcRenderer.removeListener(channel, callback)
    },
    removeAllListeners: (channel: string) => {
      ipcRenderer.removeAllListeners(channel)
    },
    invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args)
  }
})

function send(channel: string, message: string) {
  return ipcRenderer.invoke(channel, message)
}

export { sha256sum, versions, send }
