import { z } from 'zod'

// 基础类型的 zod schema 定义
const ComboSchema = z.object({
  combination: z.array(z.number()),
  similarity: z.number(),
  max: z.number().optional()
})

const PlayerMetadataSchema = z.object({
  width: z.number().positive(),
  height: z.number().positive(),
  fps: z.number().positive(),
  durationInFrames: z.number().nonnegative()
})

const RenderRequestPayloadSchema = z.object({
  id: z.string().min(1),
  inputProps: z.object({
    overlays: z.array(z.any()),
    playerMetadata: PlayerMetadataSchema
  })
})

// 导出类型（从 schema 推导）
export type Combo = z.infer<typeof ComboSchema>
export type RenderRequestPayload = z.infer<typeof RenderRequestPayloadSchema>

/**
 * 编辑器IPC方法的Schema定义
 */
export const EditorIPCSchemas = {
  /**
   * 生成组合
   */
  generateCombos: {
    input: z.object({
      limit: z.number().positive('生成数量上限必须为正数'),
      threshold: z.number().min(0).max(1, '相似度阈值必须在0-1之间'),
      matrix: z.array(z.array(z.number()))
    }),
    output: z.array(ComboSchema)
  },

  /**
   * 提取视频关键帧
   */
  extractVideoKeyFrames: {
    input: z.object({
      src: z.string().min(1, '视频源路径不能为空')
    }),
    output: z.array(z.object({
      frameNumber: z.number().nonnegative(),
      dataUrl: z.string()
    }))
  },

  /**
   * 保存编辑器状态
   */
  saveEditorState: {
    input: z.any(), // 编辑器状态可能是复杂对象，暂时使用 any
    output: z.void()
  },

  /**
   * 压缩编辑器状态数据
   */
  compressEditorState: {
    input: z.any(), // 编辑器状态可能是复杂对象，暂时使用 any
    output: z.instanceof(Buffer)
  },

  /**
   * 解压编辑器状态数据
   */
  decompressEditorState: {
    input: z.any(), // 压缩数据可能是 Buffer 或其他格式
    output: z.any() // 解压后的数据结构可能很复杂
  },

  /**
   * 上传混剪结果到服务器
   */
  uploadMixcutResult: {
    input: z.object({
      scriptId: z.string().min(1, '脚本ID不能为空'),
      data: RenderRequestPayloadSchema,
      similarity: z.number().min(0).max(1, '相似度必须在0-1之间'),
      cover: z.string().optional(),
      duration: z.number().positive().optional()
    }),
    output: z.any() // 上传结果格式可能变化，暂时使用 any
  },

  /**
   * 获取音频文件的时长
   */
  getAudioDuration: {
    input: z.string().url('必须是有效的音频URL'),
    output: z.number().nonnegative()
  }
} as const

/**
 * 编辑器IPC客户端接口（从Schema推导）
 */
export type EditorIPCClient = {
  [K in keyof typeof EditorIPCSchemas]: (
    ...args: typeof EditorIPCSchemas[K]['input'] extends z.ZodVoid
      ? []
      : [z.infer<typeof EditorIPCSchemas[K]['input']>]
  ) => Promise<z.infer<typeof EditorIPCSchemas[K]['output']>>
}
