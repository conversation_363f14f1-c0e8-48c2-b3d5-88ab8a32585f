import { z } from 'zod'

/**
 * 窗口操作IPC方法的Schema定义
 */
export const WindowIPCSchemas = {
  /**
   * 打开开发者工具
   */
  openDevTool: {
    input: z.void(),
    output: z.void()
  },

  /**
   * 最小化窗口
   */
  minimize: {
    input: z.void(),
    output: z.void()
  },

  /**
   * 最大化窗口或还原窗口
   * @returns 当前是否为最大化状态
   */
  maximize: {
    input: z.void(),
    output: z.boolean()
  },

  /**
   * 关闭窗口并退出应用
   */
  close: {
    input: z.void(),
    output: z.void()
  }
} as const

/**
 * 窗口操作IPC客户端接口（从Schema推导）
 */
export type WindowIPCClient = {
  [K in keyof typeof WindowIPCSchemas]: (
    ...args: typeof WindowIPCSchemas[K]['input'] extends z.ZodVoid
      ? []
      : [z.infer<typeof WindowIPCSchemas[K]['input']>]
  ) => Promise<z.infer<typeof WindowIPCSchemas[K]['output']>>
}
