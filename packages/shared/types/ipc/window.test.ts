import { z } from 'zod'
import { WindowIPCSchemas, WindowIPCClient } from './window.js'

// 测试 Schema 定义
console.log('测试 WindowIPCSchemas...')

// 测试输入验证
const openDevToolInput = WindowIPCSchemas.openDevTool.input.safeParse(undefined)
console.log('openDevTool 输入验证:', openDevToolInput.success) // 应该为 true

const minimizeInput = WindowIPCSchemas.minimize.input.safeParse(undefined)
console.log('minimize 输入验证:', minimizeInput.success) // 应该为 true

// 测试输出验证
const maximizeOutput = WindowIPCSchemas.maximize.output.safeParse(true)
console.log('maximize 输出验证 (boolean):', maximizeOutput.success) // 应该为 true

const maximizeOutputInvalid = WindowIPCSchemas.maximize.output.safeParse("invalid")
console.log('maximize 输出验证 (invalid):', maximizeOutputInvalid.success) // 应该为 false

// 测试类型推导
type TestClient = WindowIPCClient
const testClientMethods: keyof TestClient = 'openDevTool' // 类型检查
console.log('类型推导测试通过')

console.log('所有测试通过！')
