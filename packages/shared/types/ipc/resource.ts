import { z } from 'zod'
import { ResourceType, ResourceCacheEntry } from '../resource-cache.types.js'

// 创建 ResourceType 的 zod schema
const ResourceTypeSchema = z.nativeEnum(ResourceType)

// 创建 ResourceCacheEntry 的 zod schema
const ResourceCacheEntrySchema = z.object({
  key: z.string(),
  url: z.string(),
  type: ResourceTypeSchema,
  localPath: z.string(),
  version: z.string(),
  etag: z.string().optional(),
  lastAccessed: z.number(),
  downloadedAt: z.number(),
  size: z.number()
})

/**
 * 资源缓存IPC方法的Schema定义
 */
export const ResourceIPCSchemas = {
  /**
   * 获取资源的本地路径，如果不存在则下载并缓存
   */
  fetchOrSaveResource: {
    input: z.object({
      url: z.string().url('必须是有效的URL'),
      type: ResourceTypeSchema,
      version: z.string().optional(),
      customExt: z.string().optional()
    }),
    output: z.string()
  },

  /**
   * 清理过期或超出大小限制的缓存
   */
  cleanResource: {
    input: z.object({
      maxAgeSeconds: z.number().positive().optional(),
      maxCacheSizeMB: z.number().positive().optional()
    }).optional(),
    output: z.void()
  },

  /**
   * 根据 URL 获取缓存条目
   */
  getResource: {
    input: z.object({
      url: z.string().url('必须是有效的URL')
    }),
    output: ResourceCacheEntrySchema.optional()
  },

  /**
   * 根据 URL 获取本地缓存路径 (不触发下载)
   */
  getResourcePath: {
    input: z.object({
      url: z.string().url('必须是有效的URL'),
      type: ResourceTypeSchema
    }),
    output: z.string()
  },

  /**
   * 获取所有已缓存资源的列表
   */
  getAllResources: {
    input: z.void(),
    output: z.array(ResourceCacheEntrySchema)
  }
} as const

/**
 * 资源缓存客户端接口（从Schema推导）
 */
export type ResourceIPCClient = {
  [K in keyof typeof ResourceIPCSchemas]: (
    ...args: typeof ResourceIPCSchemas[K]['input'] extends z.ZodVoid
      ? []
      : [z.infer<typeof ResourceIPCSchemas[K]['input']>]
  ) => Promise<z.infer<typeof ResourceIPCSchemas[K]['output']>>
}
